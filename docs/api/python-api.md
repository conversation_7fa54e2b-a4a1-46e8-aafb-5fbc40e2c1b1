# Python API Documentation

The Hibernet Python API provides programmatic access to all proxy management and network testing functionality. This guide covers the complete API with examples and best practices.

## 🚀 Getting Started

### Basic Import and Setup
```python
from hiber_proxy.main import HiberProxyApp
from hiber_proxy.core.protocols import ProxyProtocol
from hiber_proxy.core.database import ProxyModel
from hiber_proxy.utils.config import Config<PERSON>anager

# Initialize the application
app = HiberProxyApp()

# Or with custom configuration
app = HiberProxyApp(config_path="custom_config.json")
```

### Configuration Management
```python
from hiber_proxy.utils.config import ConfigManager

# Load configuration
config = ConfigManager("config.json")

# Access configuration values
db_path = config.config.database.path
web_port = config.config.web.port

# Modify configuration
config.config.validation.timeout = 20
config.save_config()
```

## 🔄 Proxy Management API

### HiberProxyApp Class

#### Initialization
```python
class HiberProxyApp:
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize HiberProxy application
        
        Args:
            config_path: Path to configuration file (optional)
        """
```

#### Adding Proxies
```python
# Add a single proxy
proxy_id = app.add_proxy("http://127.0.0.1:8080")
print(f"Added proxy with ID: {proxy_id}")

# Add proxy with authentication
proxy_id = app.add_proxy("http://user:<EMAIL>:8080")

# Add SOCKS proxy
proxy_id = app.add_proxy("socks5://127.0.0.1:1080")

# Add proxy with additional metadata
from hiber_proxy.core.database import ProxyModel
proxy = ProxyModel(
    host="*************",
    port=8080,
    protocol="http",
    username="user",
    password="pass",
    country="US",
    city="New York"
)
proxy_id = app.db_manager.add_proxy(proxy)
```

#### Listing Proxies
```python
# List all proxies
proxies = app.list_proxies()
for proxy in proxies:
    print(f"{proxy['host']}:{proxy['port']} ({proxy['protocol']}) - {proxy['status']}")

# List only working proxies
working_proxies = app.list_proxies(working_only=True)

# List proxies with filters
http_proxies = app.list_proxies(protocol="http")
us_proxies = app.list_proxies(country="US")

# List with pagination
proxies_page = app.list_proxies(limit=50, offset=100)
```

#### Checking Proxy Health
```python
# Check all proxies
results = app.check_proxies()
print(f"Checked {results['total']} proxies")
print(f"Working: {results['working']}, Failed: {results['failed']}")

# Check specific protocol
results = app.check_proxies(protocol="http")

# Check with custom limits
results = app.check_proxies(limit=100, timeout=15)

# Check specific proxies
proxy_ids = [1, 2, 3, 4, 5]
results = app.check_proxies(proxy_ids=proxy_ids)
```

#### Downloading Proxies
```python
# Download from all sources
results = app.download_proxies()
print(f"Downloaded {results['total_downloaded']} proxies")

# Download specific protocol
results = app.download_proxies(protocol="http")

# Download from specific repository
results = app.download_proxies(repository="TheSpeedX/PROXY-List")

# Test sources before downloading
availability = app.test_source_availability()
for url, available in availability.items():
    print(f"{url}: {'Available' if available else 'Unavailable'}")
```

#### Exporting Proxies
```python
# Export to different formats
app.export_proxies("proxies.txt", format="txt")
app.export_proxies("proxies.json", format="json")
app.export_proxies("proxies.csv", format="csv")

# Export only working proxies
app.export_proxies("working_proxies.txt", working_only=True)

# Export specific protocol
app.export_proxies("http_proxies.txt", protocol="http")

# Export with custom filters
app.export_proxies("us_proxies.txt", country="US", working_only=True)
```

#### Statistics and Analytics
```python
# Get basic statistics
stats = app.get_statistics()
print(f"Total proxies: {stats['total_proxies']}")
print(f"Working proxies: {stats['working_proxies']}")
print(f"Success rate: {stats['success_rate']:.2%}")

# Get detailed analytics
analytics = app.get_analytics()
print(f"Protocol distribution: {analytics['protocol_distribution']}")
print(f"Country distribution: {analytics['country_distribution']}")
print(f"Performance metrics: {analytics['performance_metrics']}")

# Get proxy performance history
history = app.get_proxy_history(proxy_id=1, days=7)
for entry in history:
    print(f"{entry['timestamp']}: {entry['response_time']}ms")
```

## 🗄️ Database API

### DatabaseManager Class
```python
from hiber_proxy.core.database import DatabaseManager, ProxyModel

# Initialize database manager
db = DatabaseManager("custom_proxy.db")

# Add proxy
proxy = ProxyModel(host="127.0.0.1", port=8080, protocol="http")
proxy_id = db.add_proxy(proxy)

# Get proxy by ID
proxy = db.get_proxy(proxy_id)
print(f"Proxy: {proxy.host}:{proxy.port}")

# Update proxy
proxy.is_working = False
db.update_proxy(proxy)

# Delete proxy
db.delete_proxy(proxy_id)

# Search proxies
working_proxies = db.search_proxies(is_working=True)
http_proxies = db.search_proxies(protocol="http")
```

### ProxyModel Class
```python
from hiber_proxy.core.database import ProxyModel
from datetime import datetime

# Create proxy model
proxy = ProxyModel(
    host="*************",
    port=8080,
    protocol="http",
    username="user",
    password="pass",
    country="US",
    city="New York",
    is_working=True,
    created_at=datetime.now(),
    updated_at=datetime.now()
)

# Access properties
print(f"Proxy URL: {proxy.get_url()}")
print(f"Has auth: {proxy.has_auth()}")
print(f"Is SOCKS: {proxy.is_socks()}")
```

## 🔍 Validation and Protocol Detection

### ProxyValidator Class
```python
from hiber_proxy.core.validation import ProxyValidator

# Initialize validator
validator = ProxyValidator(timeout=10, max_retries=3)

# Validate single proxy
result = validator.validate_proxy("http://127.0.0.1:8080")
if result.is_valid:
    print(f"Proxy is working: {result.response_time}ms")
else:
    print(f"Proxy failed: {result.error}")

# Validate multiple proxies
proxies = ["http://127.0.0.1:8080", "socks5://127.0.0.1:1080"]
results = validator.validate_proxies(proxies)
for proxy, result in results.items():
    print(f"{proxy}: {'✓' if result.is_valid else '✗'}")

# Custom validation settings
validator = ProxyValidator(
    timeout=15,
    max_retries=5,
    test_url="https://httpbin.org/ip",
    test_ssl_url="https://httpbin.org/ip"
)
```

### ProtocolDetector Class
```python
from hiber_proxy.core.protocols import ProtocolDetector

# Initialize detector
detector = ProtocolDetector()

# Detect protocol
protocol = detector.detect_protocol("127.0.0.1", 8080)
print(f"Detected protocol: {protocol}")

# Check specific protocol support
supports_http = detector.supports_protocol("127.0.0.1", 8080, "http")
supports_socks5 = detector.supports_protocol("127.0.0.1", 1080, "socks5")
```

## 🌐 Web API Integration

### Flask App Integration
```python
from hiber_proxy.web.app import create_app

# Create Flask app
app, socketio = create_app("config.json")

# Access HiberProxy instance
hiber_app = app.hiber_app

# Use in custom routes
@app.route('/custom/stats')
def custom_stats():
    stats = hiber_app.get_statistics()
    return jsonify(stats)

# Run the web server
if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000)
```

### WebSocket Events
```python
from flask_socketio import emit

# Emit proxy update
def notify_proxy_update(proxy_data):
    socketio.emit('proxy_update', proxy_data)

# Emit statistics update
def notify_stats_update(stats):
    socketio.emit('stats_update', stats)
```

## 🔧 Configuration API

### ConfigManager Class
```python
from hiber_proxy.utils.config import ConfigManager

# Load configuration
config = ConfigManager("config.json")

# Access nested configuration
db_config = config.config.database
web_config = config.config.web
scraping_config = config.config.scraping

# Modify configuration
config.config.validation.timeout = 20
config.config.web.port = 8080

# Save changes
config.save_config()

# Create default configuration
config.create_default_config("new_config.json")
```

## 📊 Analytics API

### Performance Metrics
```python
# Get performance metrics
metrics = app.get_performance_metrics()
print(f"Average response time: {metrics['avg_response_time']}ms")
print(f"Success rate: {metrics['success_rate']:.2%}")
print(f"Total requests: {metrics['total_requests']}")

# Get metrics by time period
from datetime import datetime, timedelta
end_time = datetime.now()
start_time = end_time - timedelta(hours=24)
metrics = app.get_performance_metrics(start_time, end_time)
```

### Geographic Analytics
```python
# Get geographic distribution
geo_stats = app.get_geographic_stats()
for country, data in geo_stats.items():
    print(f"{country}: {data['count']} proxies, {data['success_rate']:.1%} success")

# Get city-level statistics
city_stats = app.get_city_stats("US")
for city, data in city_stats.items():
    print(f"{city}: {data['count']} proxies")
```

## 🚀 Advanced Usage

### Custom Proxy Sources
```python
from hiber_proxy.scrapers.github_scrapers import GitHubProxyScrapers

# Initialize scraper
scraper = GitHubProxyScrapers()

# Add custom source
scraper.add_custom_source(
    name="Custom Source",
    url="https://example.com/proxies.txt",
    protocol="http"
)

# Scrape from custom source
proxies = scraper.scrape_custom_source("https://example.com/proxies.txt")
```

### Memory Management
```python
from hiber_proxy.core.memory_manager import get_memory_manager

# Get memory manager
memory_manager = get_memory_manager()

# Check memory usage
usage = memory_manager.get_memory_usage()
print(f"Memory usage: {usage['used_mb']}MB / {usage['limit_mb']}MB")

# Force cleanup
memory_manager.cleanup()
```

### Batch Operations
```python
# Batch add proxies
proxy_urls = [
    "http://127.0.0.1:8080",
    "http://127.0.0.1:8081",
    "socks5://127.0.0.1:1080"
]

results = []
for url in proxy_urls:
    proxy_id = app.add_proxy(url)
    results.append(proxy_id)

print(f"Added {len(results)} proxies")

# Batch check proxies
check_results = app.check_proxies(proxy_ids=results)
print(f"Batch check completed: {check_results}")
```

## 🛡️ Error Handling

### Exception Handling
```python
from hiber_proxy.core.exceptions import (
    ProxyValidationError,
    DatabaseError,
    ConfigurationError
)

try:
    proxy_id = app.add_proxy("invalid-proxy-url")
except ProxyValidationError as e:
    print(f"Invalid proxy: {e}")
except DatabaseError as e:
    print(f"Database error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

### Logging Integration
```python
from hiber_proxy.core.logging_config import get_logger

# Get logger
logger = get_logger('my_app')

# Log operations
logger.info("Starting proxy check")
results = app.check_proxies()
logger.info(f"Check completed: {results}")
```

## 🔄 Context Managers

### Application Context
```python
# Use as context manager
with HiberProxyApp("config.json") as app:
    # App is automatically initialized and cleaned up
    stats = app.get_statistics()
    print(f"Total proxies: {stats['total_proxies']}")
# App is automatically closed
```

### Database Context
```python
# Database context manager
with DatabaseManager("proxy.db") as db:
    proxy = ProxyModel(host="127.0.0.1", port=8080, protocol="http")
    proxy_id = db.add_proxy(proxy)
# Database connection is automatically closed
```

---

**Python API Mastery!** 🎉

You now have complete programmatic control over Hibernet's functionality.
