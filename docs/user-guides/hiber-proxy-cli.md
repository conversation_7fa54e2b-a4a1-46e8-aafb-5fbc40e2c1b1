# HiberProxy CLI Guide

The HiberProxy Enhanced command-line interface provides powerful proxy management capabilities with an intuitive terminal-based experience. This guide covers all CLI operations and advanced usage patterns.

## 🚀 Getting Started

### Basic CLI Usage
```bash
# Show help and available commands
python -m hiber_proxy --help

# Show version information
python -m hiber_proxy --version

# Use custom configuration file
python -m hiber_proxy --config custom_config.json <command>
```

### Command Structure
```bash
python -m hiber_proxy [global_options] <command> [command_options]
```

## 📋 Command Reference

### Proxy Management Commands

#### Add Proxies
```bash
# Add a single HTTP proxy
python -m hiber_proxy add "http://127.0.0.1:8080"

# Add SOCKS5 proxy with authentication
python -m hiber_proxy add "socks5://user:<EMAIL>:1080"

# Add multiple proxies from file
python -m hiber_proxy add --file proxies.txt

# Add proxy with custom metadata
python -m hiber_proxy add "http://127.0.0.1:8080" --country US --city "New York"
```

#### List Proxies
```bash
# List all proxies
python -m hiber_proxy list

# List only working proxies
python -m hiber_proxy list --working-only

# List specific protocol
python -m hiber_proxy list --protocol http

# List with pagination
python -m hiber_proxy list --limit 50 --offset 100

# List with detailed information
python -m hiber_proxy list --detailed

# Filter by country
python -m hiber_proxy list --country US

# Sort by response time
python -m hiber_proxy list --sort response_time
```

#### Check Proxy Health
```bash
# Check all proxies
python -m hiber_proxy check

# Check specific number of proxies
python -m hiber_proxy check --limit 100

# Check specific protocol
python -m hiber_proxy check --protocol http

# Check with custom timeout
python -m hiber_proxy check --timeout 15

# Check specific proxy IDs
python -m hiber_proxy check --ids 1,2,3,4,5

# Parallel checking with custom concurrency
python -m hiber_proxy check --concurrent 20
```

#### Download Proxies
```bash
# Download from all GitHub sources
python -m hiber_proxy download

# Download specific protocol
python -m hiber_proxy download --protocol http

# Download from specific repository
python -m hiber_proxy download --repository "TheSpeedX/PROXY-List"

# Test sources before downloading
python -m hiber_proxy download --test-sources

# Download with validation
python -m hiber_proxy download --validate

# Download and check immediately
python -m hiber_proxy download --check-after
```

#### Export Proxies
```bash
# Export to text file
python -m hiber_proxy export --format txt --output proxies.txt

# Export to JSON with metadata
python -m hiber_proxy export --format json --output proxies.json --include-metadata

# Export to CSV
python -m hiber_proxy export --format csv --output proxies.csv

# Export only working proxies
python -m hiber_proxy export --working-only --output working_proxies.txt

# Export specific protocol
python -m hiber_proxy export --protocol socks5 --output socks_proxies.txt

# Export with compression
python -m hiber_proxy export --format json --output proxies.json.gz --compress
```

### Database Management

#### Database Operations
```bash
# Initialize database
python -m hiber_proxy init-db

# Show database statistics
python -m hiber_proxy stats

# Cleanup failed proxies
python -m hiber_proxy cleanup --max-failures 5

# Backup database
python -m hiber_proxy backup --output backup_$(date +%Y%m%d).db

# Optimize database
python -m hiber_proxy optimize

# Reset database (WARNING: Deletes all data)
python -m hiber_proxy reset-db --confirm
```

#### Migration Operations
```bash
# Migrate from legacy files
python -m hiber_proxy migrate --files old_proxies.txt --protocol http

# Migrate multiple files
python -m hiber_proxy migrate --files "*.txt" --protocol mixed

# Migrate with validation
python -m hiber_proxy migrate --files proxies.txt --validate

# Show migration statistics
python -m hiber_proxy migrate --files proxies.txt --dry-run
```

### Analytics and Reporting

#### Statistics Commands
```bash
# Show basic statistics
python -m hiber_proxy stats

# Show detailed analytics
python -m hiber_proxy analytics

# Show geographic distribution
python -m hiber_proxy analytics --geographic

# Show protocol distribution
python -m hiber_proxy analytics --protocols

# Show performance metrics
python -m hiber_proxy analytics --performance

# Export analytics to file
python -m hiber_proxy analytics --export analytics_report.json
```

#### Performance Analysis
```bash
# Show top performing proxies
python -m hiber_proxy top --metric response_time --limit 10

# Show worst performing proxies
python -m hiber_proxy worst --metric success_rate --limit 10

# Show proxy history
python -m hiber_proxy history --proxy-id 123 --days 7

# Generate performance report
python -m hiber_proxy report --output performance_report.html
```

### Configuration Management

#### Configuration Commands
```bash
# Create default configuration
python -m hiber_proxy config --create-default config.json

# Validate configuration
python -m hiber_proxy config --validate config.json

# Show current configuration
python -m hiber_proxy config --show

# Update configuration value
python -m hiber_proxy config --set validation.timeout=20

# Reset configuration to defaults
python -m hiber_proxy config --reset
```

## 🎯 Interactive Mode

### Launch Interactive Interface
```bash
# Start interactive mode
python -m hiber_proxy interactive
```

### Interactive Menu
```
╔══════════════════════════════════════════════════════════════════════════════╗
║                        HiberProxy Enhanced v2.0.0                           ║
║                     Multi-Protocol Proxy Management                         ║
╚══════════════════════════════════════════════════════════════════════════════╝

Current Status:
├─ Database: hiber_proxy.db (Connected)
├─ Total Proxies: 1,247
├─ Working Proxies: 892 (71.5%)
└─ Last Check: 2 hours ago

Available Commands:
1. Download proxies from sources
2. Check existing proxies
3. List proxies
4. Add custom proxy
5. Export proxies
6. Show statistics
7. Cleanup failed proxies
8. Configuration
9. Analytics
0. Exit

Enter your choice (0-9): _
```

### Interactive Features
- **Real-time Status**: Live proxy count and health statistics
- **Progress Indicators**: Visual progress bars for long operations
- **Error Handling**: Graceful error messages and recovery options
- **History**: Command history and quick repeat options
- **Help System**: Context-sensitive help and examples

## 🔧 Advanced Usage

### Batch Operations
```bash
# Batch add proxies with validation
cat proxy_list.txt | python -m hiber_proxy add --batch --validate

# Batch check with custom settings
python -m hiber_proxy check --batch --timeout 10 --retries 3

# Batch export by country
for country in US EU AS; do
  python -m hiber_proxy export --country $country --output ${country}_proxies.txt
done
```

### Scripting Integration
```bash
#!/bin/bash
# Automated proxy management script

# Download fresh proxies
echo "Downloading proxies..."
python -m hiber_proxy download --protocol http

# Check their health
echo "Checking proxy health..."
python -m hiber_proxy check --limit 200

# Export working proxies
echo "Exporting working proxies..."
python -m hiber_proxy export --working-only --output working_proxies.txt

# Show statistics
echo "Final statistics:"
python -m hiber_proxy stats
```

### Pipeline Operations
```bash
# Chain operations with pipes
python -m hiber_proxy list --working-only --format json | \
  jq '.[] | select(.country == "US")' | \
  python -m hiber_proxy import --format json
```

## 📊 Output Formats

### Standard Output
```bash
# Default table format
python -m hiber_proxy list
┌────┬─────────────────┬──────┬──────────┬─────────┬─────────────┬──────────────┐
│ ID │ Host            │ Port │ Protocol │ Country │ Status      │ Response Time│
├────┼─────────────────┼──────┼──────────┼─────────┼─────────────┼──────────────┤
│ 1  │ *************   │ 8080 │ http     │ US      │ ✓ Working   │ 45ms         │
│ 2  │ *********       │ 3128 │ http     │ EU      │ ✓ Working   │ 67ms         │
│ 3  │ ************    │ 1080 │ socks5   │ AS      │ ✗ Failed    │ ---          │
└────┴─────────────────┴──────┴──────────┴─────────┴─────────────┴──────────────┘
```

### JSON Output
```bash
# JSON format for scripting
python -m hiber_proxy list --format json
[
  {
    "id": 1,
    "host": "*************",
    "port": 8080,
    "protocol": "http",
    "country": "US",
    "is_working": true,
    "response_time": 45
  }
]
```

### CSV Output
```bash
# CSV format for spreadsheets
python -m hiber_proxy list --format csv
id,host,port,protocol,country,is_working,response_time
1,*************,8080,http,US,true,45
2,*********,3128,http,EU,true,67
```

## 🎨 Customization

### Custom Output Templates
```bash
# Custom format string
python -m hiber_proxy list --format custom --template "{host}:{port} ({protocol})"

# Custom table columns
python -m hiber_proxy list --columns host,port,status,response_time
```

### Color Themes
```bash
# Enable colored output
python -m hiber_proxy list --color

# Disable colors for scripting
python -m hiber_proxy list --no-color

# Custom color scheme
export HIBER_COLOR_SCHEME=dark_orange
python -m hiber_proxy list --color
```

### Verbosity Levels
```bash
# Quiet mode (errors only)
python -m hiber_proxy check --quiet

# Verbose mode (detailed output)
python -m hiber_proxy check --verbose

# Debug mode (maximum detail)
python -m hiber_proxy check --debug
```

## 🔍 Filtering and Searching

### Advanced Filtering
```bash
# Multiple filters
python -m hiber_proxy list --protocol http --country US --working-only

# Response time filtering
python -m hiber_proxy list --min-response-time 50 --max-response-time 200

# Date filtering
python -m hiber_proxy list --created-after "2024-01-01" --updated-before "2024-12-31"

# Text search
python -m hiber_proxy list --search "proxy.example.com"
```

### Complex Queries
```bash
# SQL-like filtering
python -m hiber_proxy query "SELECT * FROM proxies WHERE country='US' AND response_time < 100"

# Regular expression filtering
python -m hiber_proxy list --host-regex "^192\.168\."

# Custom filter expressions
python -m hiber_proxy list --filter "country in ['US', 'EU'] and response_time < 100"
```

## 🚨 Error Handling

### Common Error Scenarios
```bash
# Handle network errors gracefully
python -m hiber_proxy download --retry-on-error --max-retries 3

# Continue on individual failures
python -m hiber_proxy check --continue-on-error

# Detailed error reporting
python -m hiber_proxy check --error-details
```

### Debugging
```bash
# Enable debug logging
python -m hiber_proxy --debug check

# Save debug output to file
python -m hiber_proxy --debug check 2> debug.log

# Trace execution
python -m hiber_proxy --trace check
```

## 🔄 Automation

### Cron Integration
```bash
# Add to crontab for automated proxy management
# Check proxies every hour
0 * * * * /path/to/python -m hiber_proxy check --quiet

# Download new proxies daily
0 6 * * * /path/to/python -m hiber_proxy download --quiet

# Cleanup failed proxies weekly
0 0 * * 0 /path/to/python -m hiber_proxy cleanup --max-failures 10
```

### Systemd Service
```ini
# /etc/systemd/system/hiber-proxy-check.service
[Unit]
Description=HiberProxy Health Check
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/python3 -m hiber_proxy check --quiet
User=hiber
WorkingDirectory=/opt/hibernet

[Install]
WantedBy=multi-user.target
```

---

**CLI Mastery!** 🎉

You now have complete command-line control over HiberProxy Enhanced with professional-grade automation capabilities.
