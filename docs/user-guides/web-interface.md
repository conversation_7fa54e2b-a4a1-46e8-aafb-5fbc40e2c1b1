# Web Interface Guide

The Hibernet web interface provides a modern, terminal-inspired dashboard for managing proxies and monitoring network testing activities. Built with a dark orange aesthetic reminiscent of classic terminal interfaces.

## 🌐 Getting Started

### Launch Web Interface
```bash
# Start the web server
python start_web.py

# Or use the module directly
python -m hiber_proxy.web.run_server

# Custom port and host
python start_web.py --host 0.0.0.0 --port 8080
```

### Access the Interface
- **Local Access**: http://localhost:5000
- **Network Access**: http://your-ip:5000
- **Custom Port**: http://localhost:8080 (if using custom port)

## 🎨 Interface Overview

### Dark Orange Terminal Theme
The interface features a distinctive dark orange terminal aesthetic:
- **Background**: Dark charcoal (#1a1a1a)
- **Primary Orange**: Bright orange (#ff6600)
- **Secondary Orange**: Light orange (#ff8533)
- **Text**: High contrast white/orange text
- **Fonts**: Monospace fonts for authentic terminal feel

### Navigation Structure
```
┌─ Dashboard ──────────────────────────────────────────────────────────┐
│  ├─ System Overview                                                  │
│  ├─ Real-time Statistics                                             │
│  └─ Quick Actions                                                    │
├─ Proxy Management ──────────────────────────────────────────────────┤
│  ├─ Proxy List                                                       │
│  ├─ Add Proxies                                                      │
│  ├─ Health Checking                                                  │
│  └─ Import/Export                                                    │
├─ Analytics ─────────────────────────────────────────────────────────┤
│  ├─ Performance Metrics                                              │
│  ├─ Geographic Distribution                                          │
│  └─ Historical Data                                                  │
├─ Configuration ─────────────────────────────────────────────────────┤
│  ├─ System Settings                                                  │
│  ├─ Proxy Sources                                                    │
│  └─ Export Settings                                                  │
└─ Stress Testing ───────────────────────────────────────────────────┘
   ├─ Test Configuration
   ├─ Real-time Monitoring
   └─ Results Analysis
```

## 📊 Dashboard Features

### System Overview Panel
```
╔══════════════════════════════════════════════════════════════════════════════╗
║                            HIBERNET DASHBOARD                               ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ System Status: ● ONLINE          Uptime: 2h 34m 12s                        ║
║ Database: ● CONNECTED             Memory: 245MB / 1024MB                    ║
║ Web Server: ● RUNNING             CPU: 12.3%                               ║
╚══════════════════════════════════════════════════════════════════════════════╝
```

### Real-time Statistics
- **Proxy Count**: Total, working, failed proxies
- **Success Rates**: Overall and per-protocol success rates
- **Performance Metrics**: Average response times, throughput
- **Geographic Distribution**: Proxy locations on world map
- **Recent Activity**: Latest proxy additions, checks, failures

### Quick Actions
- **Download Proxies**: One-click proxy downloading from GitHub sources
- **Health Check**: Immediate proxy validation
- **Export Data**: Quick export in various formats
- **System Cleanup**: Remove failed proxies and optimize database

## 🔄 Proxy Management

### Proxy List View
```
┌─ PROXY LIST ─────────────────────────────────────────────────────────────────┐
│ Filter: [All ▼] [HTTP ▼] [Working ▼]                    Search: [_________] │
├──────────────────────────────────────────────────────────────────────────────┤
│ ● 192.168.1.100:8080    HTTP    US    ✓ Working    45ms    99.2%    [Test] │
│ ● 10.0.0.50:3128        HTTP    EU    ✓ Working    67ms    97.8%    [Test] │
│ ● 203.45.67.89:1080     SOCKS5  AS    ✗ Failed     ---     0.0%     [Test] │
│ ● proxy.example.com:80  HTTP    US    ✓ Working    23ms    100%     [Test] │
├──────────────────────────────────────────────────────────────────────────────┤
│ Showing 4 of 1,247 proxies                                    [Add] [Import] │
└──────────────────────────────────────────────────────────────────────────────┘
```

### Add Proxy Interface
```
┌─ ADD PROXY ──────────────────────────────────────────────────────────────────┐
│                                                                              │
│ Proxy URL: [http://127.0.0.1:8080________________________] [Validate]       │
│                                                                              │
│ Protocol:  [HTTP ▼]                                                         │
│ Username:  [_________________________] (optional)                           │
│ Password:  [_________________________] (optional)                           │
│ Country:   [US ▼] (optional)                                                │
│                                                                              │
│ [Test Connection] [Add Proxy] [Cancel]                                      │
└──────────────────────────────────────────────────────────────────────────────┘
```

### Bulk Import Interface
- **File Upload**: Drag-and-drop or browse for proxy files
- **Format Detection**: Automatic detection of proxy file formats
- **Preview**: Show detected proxies before import
- **Validation**: Optional validation during import
- **Progress Tracking**: Real-time import progress

## 📈 Analytics Dashboard

### Performance Metrics
```
┌─ PERFORMANCE METRICS ───────────────────────────────────────────────────────┐
│                                                                              │
│ Success Rate Trend:                                                          │
│ 100% ┤                                                                       │
│  90% ┤     ╭─╮                                                               │
│  80% ┤   ╭─╯ ╰─╮                                                             │
│  70% ┤ ╭─╯     ╰─╮                                                           │
│  60% ┤─╯         ╰─                                                          │
│      └─────────────────────────────────────────────────────────────────────│
│      12:00  13:00  14:00  15:00  16:00  17:00  18:00                       │
│                                                                              │
│ Response Time Distribution:                                                  │
│ < 50ms:  ████████████████████████████████████████ 65%                      │
│ 50-100ms: ████████████████████ 35%                                          │
│ 100-200ms: ████ 8%                                                          │
│ > 200ms: ██ 2%                                                              │
└──────────────────────────────────────────────────────────────────────────────┘
```

### Geographic Distribution
- **World Map**: Interactive map showing proxy locations
- **Country Statistics**: Proxy count and performance by country
- **Regional Performance**: Success rates by geographic region
- **Latency Heatmap**: Response time visualization by location

### Protocol Analysis
- **Protocol Distribution**: HTTP vs SOCKS proxy breakdown
- **Protocol Performance**: Success rates by protocol type
- **Port Analysis**: Most common proxy ports and their performance
- **Authentication Stats**: Authenticated vs anonymous proxy performance

## ⚙️ Configuration Management

### System Settings
```
┌─ SYSTEM CONFIGURATION ──────────────────────────────────────────────────────┐
│                                                                              │
│ Database Settings:                                                           │
│ ├─ Database Path: [hiber_proxy.db_______________] [Browse]                   │
│ ├─ Pool Size: [10___] connections                                           │
│ └─ Timeout: [30___] seconds                                                 │
│                                                                              │
│ Web Interface:                                                               │
│ ├─ Host: [0.0.0.0_______________]                                           │
│ ├─ Port: [5000___]                                                          │
│ └─ Debug Mode: [☐] Enable                                                   │
│                                                                              │
│ Validation Settings:                                                         │
│ ├─ Timeout: [10___] seconds                                                 │
│ ├─ Max Retries: [3___]                                                      │
│ └─ Test URL: [http://httpbin.org/ip_______________]                         │
│                                                                              │
│ [Save Configuration] [Reset to Defaults] [Export Config]                    │
└──────────────────────────────────────────────────────────────────────────────┘
```

### Proxy Sources Management
- **GitHub Sources**: Configure GitHub proxy list URLs
- **Custom Sources**: Add custom proxy sources
- **Source Priority**: Set download priority for sources
- **Auto-Update**: Configure automatic proxy updates
- **Source Validation**: Test source availability

## 🚀 Stress Testing Interface

### Test Configuration
```
┌─ STRESS TEST CONFIGURATION ─────────────────────────────────────────────────┐
│                                                                              │
│ Target Configuration:                                                        │
│ ├─ Target URL: [https://httpbin.org/get_______________] [Validate]          │
│ ├─ Protocol: [HTTP/2 ▼]                                                     │
│ └─ Test Type: [HTTP Flood ▼]                                               │
│                                                                              │
│ Load Configuration:                                                          │
│ ├─ Threads: [500___]                                                        │
│ ├─ Duration: [60___] seconds                                                │
│ └─ Rate Limit: [____] req/sec (optional)                                   │
│                                                                              │
│ Proxy Configuration:                                                         │
│ ├─ Use Proxies: [☑] Enable                                                 │
│ ├─ Rotation Strategy: [Health Based ▼]                                     │
│ └─ Max Proxies: [100___]                                                    │
│                                                                              │
│ [Start Test] [Save Configuration] [Load Configuration]                      │
└──────────────────────────────────────────────────────────────────────────────┘
```

### Real-time Test Monitoring
- **Live Statistics**: Real-time request rates, success rates, response times
- **Progress Indicators**: Test progress and remaining time
- **Error Tracking**: Live error categorization and counts
- **Performance Graphs**: Real-time charts of key metrics
- **Proxy Status**: Live proxy health and usage statistics

## 🔌 WebSocket Features

### Real-time Updates
The interface uses WebSocket connections for real-time updates:
- **Connection Status**: Live connection indicator
- **Auto-Reconnection**: Automatic reconnection on connection loss
- **Update Frequency**: Configurable update intervals
- **Data Streaming**: Efficient real-time data streaming

### Event Types
- **Proxy Updates**: New proxies, status changes, health updates
- **System Events**: Configuration changes, errors, warnings
- **Test Events**: Stress test progress, results, completion
- **Statistics Updates**: Real-time metric updates

## 📱 Responsive Design

### Mobile Support
- **Responsive Layout**: Adapts to different screen sizes
- **Touch-Friendly**: Optimized for touch interactions
- **Mobile Navigation**: Collapsible navigation menu
- **Simplified Views**: Streamlined mobile interface

### Browser Compatibility
- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **WebSocket Support**: Required for real-time features
- **JavaScript**: Required for full functionality
- **CSS Grid/Flexbox**: Modern layout support

## 🔧 API Integration

### RESTful API Access
The web interface provides full API access:
```javascript
// Get proxy statistics
fetch('/api/stats')
  .then(response => response.json())
  .then(data => console.log(data));

// Add a proxy
fetch('/api/proxies', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({proxy: 'http://127.0.0.1:8080'})
});
```

### WebSocket API
```javascript
// Connect to WebSocket
const socket = io();

// Listen for proxy updates
socket.on('proxy_update', (data) => {
  console.log('Proxy updated:', data);
});

// Listen for statistics updates
socket.on('stats_update', (data) => {
  updateDashboard(data);
});
```

## 🛡️ Security Features

### Authentication (Future)
- **User Management**: Multi-user support
- **Role-Based Access**: Different permission levels
- **Session Management**: Secure session handling
- **API Keys**: Programmatic access control

### Security Headers
- **CORS Configuration**: Cross-origin request handling
- **Content Security Policy**: XSS protection
- **Rate Limiting**: API abuse prevention
- **Input Validation**: Comprehensive input sanitization

## 🎯 Best Practices

### Performance Optimization
- **Pagination**: Large datasets are paginated
- **Lazy Loading**: Components load on demand
- **Caching**: Intelligent data caching
- **Compression**: Gzip compression for responses

### User Experience
- **Loading Indicators**: Clear feedback for long operations
- **Error Handling**: Graceful error messages
- **Keyboard Shortcuts**: Power user shortcuts
- **Accessibility**: Screen reader support

---

**Web Interface Mastery!** 🎉

You now have complete control over Hibernet through the powerful web interface.
