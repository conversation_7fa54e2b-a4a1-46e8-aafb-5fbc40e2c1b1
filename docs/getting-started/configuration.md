# Configuration Guide

This guide covers all configuration options for Hibernet, including file-based configuration, environment variables, and runtime settings.

## 📋 Configuration Overview

Hibernet uses a hierarchical configuration system:
1. **Default values** - Built-in sensible defaults
2. **Configuration files** - JSON/YAML files
3. **Environment variables** - Runtime overrides
4. **Command-line arguments** - Highest priority

## 🔧 Configuration Files

### Main Configuration File

Create a configuration file using the built-in generator:
```bash
# Generate default configuration
python -m hiber_proxy config --create-default config.json
```

### Configuration File Structure

```json
{
  "database": {
    "path": "hiber_proxy.db",
    "pool_size": 10,
    "timeout": 30,
    "backup_enabled": true,
    "backup_interval": 3600
  },
  "scraping": {
    "enabled": true,
    "max_concurrent": 10,
    "timeout": 30,
    "retry_attempts": 3,
    "user_agent": "HiberProxy/2.0",
    "github_sources": [
      "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
      "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"
    ]
  },
  "validation": {
    "timeout": 10,
    "max_retries": 3,
    "test_url": "http://httpbin.org/ip",
    "test_ssl_url": "https://httpbin.org/ip",
    "concurrent_checks": 50,
    "check_interval": 300
  },
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": false,
    "secret_key": "your-secret-key-here",
    "cors_enabled": true,
    "websocket_enabled": true
  },
  "logging": {
    "level": "INFO",
    "file": "logs/hiber_proxy.log",
    "max_size": "10MB",
    "backup_count": 5,
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  },
  "memory": {
    "max_memory_mb": 1024,
    "cleanup_threshold": 0.8,
    "gc_interval": 60
  },
  "export": {
    "default_format": "txt",
    "include_stats": true,
    "compression": false
  }
}
```

### YAML Configuration (Alternative)

```yaml
# config.yaml
database:
  path: hiber_proxy.db
  pool_size: 10
  timeout: 30

scraping:
  enabled: true
  max_concurrent: 10
  timeout: 30
  github_sources:
    - "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt"
    - "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"

validation:
  timeout: 10
  max_retries: 3
  test_url: "http://httpbin.org/ip"

web:
  host: "0.0.0.0"
  port: 5000
  debug: false
```

## 🌍 Environment Variables

Override configuration using environment variables:

### Database Configuration
```bash
export HIBER_DB_PATH="custom_proxy.db"
export HIBER_DB_TIMEOUT="60"
export HIBER_DB_POOL_SIZE="20"
```

### Web Interface Configuration
```bash
export HIBER_WEB_HOST="127.0.0.1"
export HIBER_WEB_PORT="8080"
export HIBER_WEB_DEBUG="true"
export HIBER_WEB_SECRET_KEY="your-production-secret"
```

### Scraping Configuration
```bash
export HIBER_SCRAPING_ENABLED="true"
export HIBER_SCRAPING_TIMEOUT="45"
export HIBER_SCRAPING_MAX_CONCURRENT="15"
```

### Validation Configuration
```bash
export HIBER_VALIDATION_TIMEOUT="15"
export HIBER_VALIDATION_MAX_RETRIES="5"
export HIBER_VALIDATION_TEST_URL="https://httpbin.org/ip"
```

### Logging Configuration
```bash
export HIBER_LOG_LEVEL="DEBUG"
export HIBER_LOG_FILE="logs/debug.log"
```

## 🎯 HibernetV3.0 Configuration

### Command-Line Configuration
```bash
# Basic configuration
python HibernetV3.0.py \
  --config-file stress_config.json \
  --threads 1000 \
  --duration 120 \
  --rate-limit 100

# Protocol configuration
python HibernetV3.0.py \
  --protocol http2 \
  --test-type http_flood \
  --user-agents-file custom_agents.txt
```

### Stress Testing Configuration File
```json
{
  "stress_testing": {
    "default_threads": 800,
    "default_duration": 60,
    "max_threads": 5000,
    "rate_limit": null,
    "protocols": ["http1", "http2", "http3", "websocket"],
    "user_agents": [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    ],
    "custom_headers": {
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
      "Accept-Language": "en-US,en;q=0.5",
      "Accept-Encoding": "gzip, deflate"
    }
  },
  "proxy_rotation": {
    "strategy": "health_based",
    "health_check_interval": 60,
    "failure_threshold": 3,
    "geographic_preference": ["US", "EU", "AS"]
  },
  "reporting": {
    "real_time_dashboard": true,
    "dashboard_interval": 3.0,
    "export_formats": ["json", "csv"],
    "metrics_retention": 7200
  }
}
```

## 🔐 Security Configuration

### Web Interface Security
```json
{
  "web": {
    "secret_key": "generate-a-strong-secret-key",
    "session_timeout": 3600,
    "rate_limiting": {
      "enabled": true,
      "requests_per_minute": 60,
      "burst_limit": 10
    },
    "cors": {
      "enabled": true,
      "origins": ["http://localhost:3000", "https://yourdomain.com"],
      "methods": ["GET", "POST", "PUT", "DELETE"],
      "headers": ["Content-Type", "Authorization"]
    },
    "ssl": {
      "enabled": false,
      "cert_file": "ssl/cert.pem",
      "key_file": "ssl/key.pem"
    }
  }
}
```

### Database Security
```json
{
  "database": {
    "encryption": {
      "enabled": false,
      "key_file": "encryption.key"
    },
    "backup": {
      "enabled": true,
      "interval": 3600,
      "retention_days": 7,
      "compression": true
    }
  }
}
```

## 📊 Performance Configuration

### Memory Management
```json
{
  "memory": {
    "max_memory_mb": 2048,
    "cleanup_threshold": 0.8,
    "gc_interval": 60,
    "proxy_cache_size": 10000,
    "stats_cache_size": 1000
  }
}
```

### Database Performance
```json
{
  "database": {
    "pool_size": 20,
    "timeout": 60,
    "pragma_settings": {
      "journal_mode": "WAL",
      "synchronous": "NORMAL",
      "cache_size": 10000,
      "temp_store": "MEMORY"
    }
  }
}
```

### Scraping Performance
```json
{
  "scraping": {
    "max_concurrent": 20,
    "timeout": 45,
    "retry_attempts": 5,
    "backoff_factor": 2.0,
    "connection_pool_size": 100
  }
}
```

## 🌐 Proxy Source Configuration

### GitHub Sources
```json
{
  "scraping": {
    "github_sources": [
      {
        "url": "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
        "protocol": "http",
        "enabled": true,
        "priority": 1
      },
      {
        "url": "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
        "protocol": "mixed",
        "enabled": true,
        "priority": 2
      }
    ],
    "custom_sources": [
      {
        "name": "Custom HTTP Source",
        "url": "https://your-proxy-source.com/proxies.txt",
        "protocol": "http",
        "format": "ip:port",
        "enabled": true
      }
    ]
  }
}
```

### Source Validation
```json
{
  "source_validation": {
    "test_on_download": true,
    "max_test_proxies": 10,
    "timeout": 30,
    "required_success_rate": 0.1
  }
}
```

## 📝 Logging Configuration

### Detailed Logging Setup
```json
{
  "logging": {
    "version": 1,
    "disable_existing_loggers": false,
    "formatters": {
      "standard": {
        "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
      },
      "detailed": {
        "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
      }
    },
    "handlers": {
      "console": {
        "class": "logging.StreamHandler",
        "level": "INFO",
        "formatter": "standard"
      },
      "file": {
        "class": "logging.handlers.RotatingFileHandler",
        "level": "DEBUG",
        "formatter": "detailed",
        "filename": "logs/hiber_proxy.log",
        "maxBytes": 10485760,
        "backupCount": 5
      }
    },
    "loggers": {
      "hiber_proxy": {
        "level": "DEBUG",
        "handlers": ["console", "file"],
        "propagate": false
      }
    }
  }
}
```

## 🔄 Configuration Loading

### Loading Priority
1. **Command-line arguments** (highest priority)
2. **Environment variables**
3. **Configuration file**
4. **Default values** (lowest priority)

### Configuration File Locations
The system searches for configuration files in this order:
1. Path specified by `--config` argument
2. `./config.json` (current directory)
3. `./config.yaml` (current directory)
4. `~/.hiber_proxy/config.json` (user home)
5. `/etc/hiber_proxy/config.json` (system-wide)

### Runtime Configuration Changes
```python
# Python API for runtime configuration
from hiber_proxy.utils.config import ConfigManager

config = ConfigManager('config.json')
config.config.validation.timeout = 20
config.save_config()
```

## ✅ Configuration Validation

### Validate Configuration
```bash
# Validate configuration file
python -m hiber_proxy config --validate config.json

# Test configuration with dry run
python -m hiber_proxy --config config.json --dry-run list
```

### Configuration Schema
The configuration follows a strict schema with validation:
- **Required fields**: Database path, web port
- **Type validation**: Integers, booleans, strings, arrays
- **Range validation**: Positive numbers, valid URLs
- **Format validation**: File paths, network addresses

## 🚀 Production Configuration

### Recommended Production Settings
```json
{
  "database": {
    "pool_size": 50,
    "timeout": 60,
    "backup_enabled": true
  },
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": false,
    "secret_key": "production-secret-key"
  },
  "logging": {
    "level": "INFO",
    "file": "/var/log/hiber_proxy/app.log"
  },
  "memory": {
    "max_memory_mb": 4096,
    "cleanup_threshold": 0.7
  }
}
```

### Environment-Specific Configurations
```bash
# Development
cp config.dev.json config.json

# Staging  
cp config.staging.json config.json

# Production
cp config.prod.json config.json
```

---

**Configuration Complete!** 🎉

Your Hibernet installation is now properly configured for your specific needs.
