# Quick Start Guide

Get up and running with Hibernet in just a few minutes! This guide covers the essential operations for both network stress testing and proxy management.

## 🚀 First Steps

### 1. Verify Installation
```bash
# Check if everything is installed correctly
python HibernetV3.0.py --help
python -m hiber_proxy --help
```

### 2. Start Web Interface (Optional)
```bash
# Launch the web interface for easy management
python start_web.py

# Access at: http://localhost:5000
```

## 🎯 HibernetV3.0 - Network Stress Testing

### Basic HTTP Stress Test
```bash
# Simple stress test with 100 threads for 30 seconds
python HibernetV3.0.py --target https://httpbin.org/get --threads 100 --duration 30
```

### Advanced Stress Test with Real-time Dashboard
```bash
# Advanced test with live monitoring
python HibernetV3.0.py \
  --target https://httpbin.org/get \
  --threads 500 \
  --duration 60 \
  --real-time-dashboard \
  --export-json results.json
```

### Multiple Target Testing
```bash
# Create a targets file
echo "https://httpbin.org/get" > targets.txt
echo "https://httpbin.org/post" >> targets.txt

# Test multiple targets
python HibernetV3.0.py --target-file targets.txt --threads 200
```

### Protocol-Specific Testing
```bash
# HTTP/2 stress test
python HibernetV3.0.py --target https://http2.akamai.com --protocol http2

# WebSocket stress test
python HibernetV3.0.py --target ws://echo.websocket.org --test-type websocket
```

## 🔄 HiberProxy Enhanced - Proxy Management

### Command Line Interface

#### Add Proxies Manually
```bash
# Add a single HTTP proxy
python -m hiber_proxy add "http://127.0.0.1:8080"

# Add a SOCKS5 proxy with authentication
python -m hiber_proxy add "socks5://user:<EMAIL>:1080"
```

#### Download Proxies from GitHub
```bash
# Download HTTP proxies from GitHub sources
python -m hiber_proxy download --protocol http

# Download all proxy types
python -m hiber_proxy download

# Test source availability first
python -m hiber_proxy download --test-sources
```

#### List and Check Proxies
```bash
# List all proxies
python -m hiber_proxy list

# List only working proxies
python -m hiber_proxy list --working-only

# Check proxy health
python -m hiber_proxy check --limit 50
```

#### Export Proxies
```bash
# Export to different formats
python -m hiber_proxy export --format txt --output proxies.txt
python -m hiber_proxy export --format json --output proxies.json
python -m hiber_proxy export --format csv --output proxies.csv
```

### Interactive Mode
```bash
# Launch interactive menu
python -m hiber_proxy interactive
```

The interactive mode provides a user-friendly menu:
```
╔══════════════════════════════════════════════════════════════════════════════╗
║                        HiberProxy Enhanced v2.0.0                           ║
║                     Multi-Protocol Proxy Management                         ║
╚══════════════════════════════════════════════════════════════════════════════╝

1. Download proxies from sources
2. Check existing proxies  
3. List proxies
4. Add custom proxy
5. Export proxies
6. Show statistics
7. Cleanup failed proxies
8. Exit

Enter your choice (1-8):
```

## 🌐 Web Interface Usage

### Access the Dashboard
1. Start the web server: `python start_web.py`
2. Open browser to: http://localhost:5000
3. Navigate through the dark orange terminal-themed interface

### Key Web Features
- **Dashboard**: Real-time statistics and system overview
- **Proxy Management**: Add, check, and manage proxies
- **Analytics**: Performance metrics and charts
- **Configuration**: System settings and preferences
- **Stress Testing**: Web-based stress test launcher

### API Endpoints
```bash
# Get proxy statistics
curl http://localhost:5000/api/stats

# List proxies
curl http://localhost:5000/api/proxies

# Add a proxy via API
curl -X POST http://localhost:5000/api/proxies \
  -H "Content-Type: application/json" \
  -d '{"proxy": "http://127.0.0.1:8080"}'
```

## 🔧 Configuration

### Create Default Configuration
```bash
# Generate default config file
python -m hiber_proxy config --create-default config.json
```

### Basic Configuration Example
```json
{
  "database": {
    "path": "hiber_proxy.db",
    "timeout": 30
  },
  "scraping": {
    "enabled": true,
    "max_concurrent": 10,
    "timeout": 30
  },
  "validation": {
    "timeout": 10,
    "max_retries": 3
  },
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": false
  }
}
```

## 📊 Common Workflows

### Workflow 1: Basic Proxy Testing
```bash
# 1. Download fresh proxies
python -m hiber_proxy download --protocol http

# 2. Check their health
python -m hiber_proxy check --limit 100

# 3. Export working proxies
python -m hiber_proxy export --format txt --working-only
```

### Workflow 2: Stress Testing with Proxies
```bash
# 1. Prepare proxy list
python -m hiber_proxy export --format txt --output proxies.txt --working-only

# 2. Run stress test with proxies
python HibernetV3.0.py \
  --target https://httpbin.org/ip \
  --proxy-file proxies.txt \
  --proxy-rotation health_based \
  --threads 200 \
  --duration 60
```

### Workflow 3: Web-Based Management
```bash
# 1. Start web interface
python start_web.py

# 2. Open browser to http://localhost:5000
# 3. Use web interface for:
#    - Downloading proxies
#    - Checking proxy health
#    - Viewing analytics
#    - Managing configuration
```

## 🎯 Example Scenarios

### Scenario 1: Testing Your Web Server
```bash
# Test your local development server
python HibernetV3.0.py \
  --target http://localhost:3000 \
  --threads 50 \
  --duration 30 \
  --real-time-dashboard
```

### Scenario 2: Load Testing with Geographic Distribution
```bash
# Use proxies from different countries
python -m hiber_proxy download --protocol http
python HibernetV3.0.py \
  --target https://your-api.com/endpoint \
  --proxy-file proxies.txt \
  --proxy-rotation geographic \
  --threads 100
```

### Scenario 3: WebSocket Application Testing
```bash
# Test WebSocket connections
python HibernetV3.0.py \
  --target ws://your-websocket-server.com/chat \
  --test-type websocket \
  --connections 50 \
  --duration 120
```

## 🔍 Monitoring and Analysis

### Real-time Monitoring
```bash
# Enable real-time dashboard
python HibernetV3.0.py \
  --target https://httpbin.org/get \
  --threads 200 \
  --real-time-dashboard \
  --dashboard-interval 2
```

### Export Results
```bash
# Export comprehensive results
python HibernetV3.0.py \
  --target https://httpbin.org/get \
  --threads 100 \
  --duration 60 \
  --export-json detailed_report.json \
  --export-csv metrics.csv
```

### View Statistics
```bash
# Get proxy statistics
python -m hiber_proxy stats

# Get detailed analytics
python -m hiber_proxy analytics --detailed
```

## ⚠️ Important Notes

### Security Considerations
- **Only test systems you own or have explicit permission to test**
- Start with low thread counts and short durations
- Monitor system resources during testing
- Use rate limiting to prevent overload

### Performance Tips
- Use SSD storage for better database performance
- Increase system file descriptor limits for high thread counts
- Monitor memory usage with large proxy lists
- Use geographic proxy rotation for distributed testing

## 🚀 Next Steps

Now that you're familiar with the basics:

1. **[Configuration Guide](configuration.md)** - Customize your setup
2. **[User Guides](../user-guides/)** - Detailed component documentation
3. **[API Documentation](../api/)** - Programmatic access
4. **[Examples](../examples/)** - Advanced usage scenarios

## 🆘 Need Help?

- **Documentation**: Check the detailed [user guides](../user-guides/)
- **Configuration**: See the [configuration reference](../configuration/)
- **API**: Review the [API documentation](../api/)
- **Issues**: Report problems on GitHub

---

**You're Ready to Go!** 🎉

Start with simple tests and gradually explore more advanced features as you become comfortable with the tools.
